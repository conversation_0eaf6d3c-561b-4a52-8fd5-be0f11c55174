<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meet Our Creative Engineers</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet">
    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/public.css">
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
      rel="stylesheet">
    <link
      href="https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap"
      rel="stylesheet">
    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Belleza&display=swap"
      rel="stylesheet">
    <link
      href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap"
      rel="stylesheet">
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
      rel="stylesheet">
    <link
      href="https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap"
      rel="stylesheet">
    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/darkMode.css">
    <link rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">

  </head>
  <body>
    <style>

 

      .dropdown {
          position: relative;
      }

      .dropdown-menu {
          position: absolute;
          top: 100%;
          left: 0;
          background: white;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border-radius: 5px;
          display: none;
          flex-direction: column;
          min-width: 150px;
      }

      .dropdown-menu a {
          display: block;
          padding: 10px;
          color: black;
          text-decoration: none;
      }

      .dropdown-menu a:hover {
          background: #f4f4f4;
      }


      .dropdown:hover .dropdown-menu {
          display: flex;
      }

      .btn-gold {
        background: linear-gradient(135deg, #ffd700 0%, #d4af37 100%) !important;
        color: #222 !important;
        border: 2px solid #bfa14a !important;
        font-weight: bold !important;
        transition: all 0.3s ease !important;
      }
      .btn-gold:hover, .btn-gold:focus {
        background: linear-gradient(135deg, #ffe066 0%, #d4af37 100%) !important;
        color: #000 !important;
        box-shadow: 0 3px 10px rgba(218, 165, 32, 0.2) !important;
      }

      .badge-gold {
        background: linear-gradient(135deg, #ffd700 0%, #d4af37 100%) !important;
        color: #222 !important;
        border: 1px solid #bfa14a !important;
        font-weight: bold !important;
        font-size: 15px;
        box-shadow: 0 2px 6px rgba(218, 165, 32, 0.08);
        padding: 6px 18px;
        border-radius: 20px;
        display: inline-block;
        margin-bottom: 6px;
        letter-spacing: 0.5px;
        transition: all 0.3s;
      }
      .badge-gold:hover {
        background: linear-gradient(135deg, #ffe066 0%, #d4af37 100%) !important;
        color: #000 !important;
        box-shadow: 0 4px 12px rgba(218, 165, 32, 0.15);
      }


      .logo-hero {
    background: linear-gradient(90deg, #d4af37 0%, #ffd700 50%, #bfa14a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    font-weight: bold;
}
    </style>
    <div class="container" data-aos="zoom-in" data-aos-duration="1200">
      <div class="row">
        <div
          class="navv-bar py-4 d-flex justify-content-between align-items-center">
          <div class="logo d-flex align-items-center" style="font-size: 35px;"
            data-aos="fade-down"
            data-aos-duration="1200">

            <button class="btn btn-outline-dark ms-auto "
              onclick="history.back()">
              <i class="fa-solid fa-arrow-left"></i> Back
            </button>

            <span class="logo-hero  ms-3">Decore&More</span>
          </div>
          <div class="menu-btn d-lg-none" data-aos="fade-up"
            data-aos-duration="1200">
            <i class="fa-solid fa-bars"></i>
          </div>
          <div class="nav-links" data-aos="fade-up" data-aos-duration="1200">
            <ul class="list-unstyled m-0">
              <li><a href="/" class="nav-link">Home</a></li>
              <li><a href="/#op" class="nav-link">About</a></li>
              <li class="dropdown">
                <a href="/#od" class="nav-link">Services</a>
                <div class="dropdown-menu">
                  <a href="/packages/by-occasion?occasion=Birthday"
                    class="nav-link">Birthday</a>
                  <a href="/packages/by-occasion?occasion=Wedding"
                    class="nav-link">Wedding</a>
                  <a href="/packages/by-occasion?occasion=Engagement"
                    class="nav-link">Engagement</a>
                  <a href="/packages/by-occasion?occasion=BabyShower"
                    class="nav-link">BabyShower</a>
                </div>
              </li>
              <li><a href="/designers" class="nav-link">Designers</a></li>
              <% if (!user || !user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link">Register</a>
                <div class="dropdown-menu">
                  <a href="/registerCustomer">Customer</a>
                  <a href="/register">Engineer</a>
                </div>
              </li>
              <% } %>

              <% if (!user || !user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link">Login</a>
                <div class="dropdown-menu">
                  <a href="/login">Customer</a>
                  <a href="/login">Engineer</a>
                </div>
              </li>
              <% } %>

              <li><a href="/contact" class="nav-link">Contact</a></li>

              <% if (user && user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link d-flex align-items-center">
                  <i class="fa-solid fa-user"></i>
                  <span><%= user.name %></span>
                </a>
                <div class="dropdown-menu">
                  <a href="/userProfile/<%= user.id %>" class="nav-link">
                    <i class="fas fa-user me-2"></i>Profile
                  </a>
                  <a href="#" class="nav-link" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                  </a>
                </div>
              </li>
              <% }else{ %>

              <% } %>
              <button onclick="toggleDarkMode()" class="dark-mode"
                aria-label="Toggle dark mode">
                <i class="fa-solid fa-moon" aria-hidden="true"
                  title="Toggle to dark mode"></i>
              </button>

            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="hero-section py-5 text-center">
      <div class="container">
        <h1 class="display-4 mb-4 section-gradient-title">Meet Our Creative
          Engineers</h1>
        <p class="lead mx-auto" style="max-width: 800px;">Meet the creative
          minds behind unforgettable celebrations! Here, you'll find a curated
          collection of talented engineers, each with a unique touch to make
          your special moments extraordinary.</p>
      </div>
    </div>

    <div class="container py-5">
      <!-- Add specialization filter -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <h5 class="card-title mb-3 section-gradient-title">Filter by
                Specialization</h5>
              <form id="specializationFilter" class="d-flex flex-wrap gap-2">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox"
                    name="specializations" value="Birthday" id="birthday"
                    <%=selectedSpecializations.includes('Birthday') ? 'checked'
                    : '' %>>
                  <label class="form-check-label"
                    for="birthday">Birthday</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox"
                    name="specializations" value="Wedding" id="wedding"
                    <%=selectedSpecializations.includes('Wedding') ? 'checked' :
                    '' %>>
                  <label class="form-check-label" for="wedding">Wedding</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox"
                    name="specializations" value="Engagement" id="engagement"
                    <%=selectedSpecializations.includes('Engagement') ?
                    'checked' : '' %>>
                  <label class="form-check-label"
                    for="engagement">Engagement</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox"
                    name="specializations" value="BabyShower" id="babyshower"
                    <%=selectedSpecializations.includes('BabyShower') ?
                    'checked' : '' %>>
                  <label class="form-check-label" for="babyshower">Baby
                    Shower</label>
                </div>
                <button type="submit" class="btn btn-gold ms-auto">Apply
                  Filters</button>
              </form>
            </div>
          </div>
        </div>
      </div>

      <div class="row g-4">
        <h1 class="text-center py-5 section-gradient-title">Our Talented
          Engineers</h1>
        <% if (engineers.length === 0) { %>
        <div class="col-12 text-center">
          <p class="lead">No engineers found matching the selected
            specializations.</p>
        </div>
        <% } else { %>
        <% engineers.forEach(engineer => { %>
        <div class="col-12 col-md-6 col-lg-4">
          <div class="designer-card h-100">
            <div class="card-body text-center">
              <div class="profile-img-wrapper mb-3">
                <img
                  src="<%= engineer.profilePhoto || 'https://via.placeholder.com/200' %>"
                  alt="<%= engineer.name %>" class="profile-img">
              </div>
              <h3 class="designer-name"><%= engineer.firstName %> <%=
                engineer.lastName %></h3>
              <div class="specialties mb-2">
                <% engineer.specialties.forEach(specialty => { %>
                <span class="badge badge-gold me-1"><%= specialty %></span>
                <% }) %>
              </div>
              <div class="rating mb-2">
                <% let rating = Math.round(engineer.averageRating); %>
                <% for (let i = 0; i < 5; i++) { %>
                <% if (i < rating) { %>
                <i class="bi bi-star-fill"></i>
                <% } else { %>
                <i class="bi bi-star"></i>
                <% } %>
                <% } %>
                <span class="ms-2"
                  style="font-family: sans-serif; font-size: 18px;"><%=
                  engineer.rating %></span>
              </div>
              <p class="designer-bio"><%= engineer.bio %></p>
              <a href="/profile/<%= engineer._id %>">
                <button class="btn"
                  style="background-color: goldenrod; color:white; font-family: sans-serif;">View
                  Full Profile</button>
              </a>
            </div>
          </div>
        </div>
        <% }) %>
        <% } %>
      </div>
    </div>

    <footer class="bg-dark text-warning py-5">
      <div class="container">
        <div class="row g-4">
          <div class="col-12 text-center mb-4">
            <h2 class="display-4 fw-bold section-gradient-title">Decor&More</h2>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Contact Us</h5>
            <div class="mt-3">
              <p><i
                  class="bi bi-envelope-fill me-2"></i><EMAIL></p>
              <a href="https://web.whatsapp.com/send?phone=201556159175"
                target="_blank">
                <p><i class="bi bi-telephone-fill me-2"></i>+20 1556159175</p>
              </a>
              <p><i class="bi bi-geo-alt-fill me-2"></i>123 Decor Street,
                Design
                City</p>
            </div>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Quick Links</h5>
            <ul class="list-unstyled mt-3">
              <li class="mb-2"><a href="/"
                  class="text-warning text-decoration-none">Home</a></li>
              <li class="mb-2"><a href="/#op"
                  class="text-warning text-decoration-none">About</a></li>
              <% if (!user) { %>
              <li class="mb-2"><a href="/login"
                  class="text-warning text-decoration-none">login</a></li>
              <li class="mb-2"><a href="/register"
                  class="text-warning text-decoration-none">register</a></li>
              <% } %>
            </ul>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Follow Us</h5>
            <div class="mt-3 d-flex gap-3">
              <a href="#" class="text-warning fs-4" aria-label="Facebook"><i
                  class="bi bi-facebook"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="Instagram"><i
                  class="bi bi-instagram"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="Twitter"><i
                  class="bi bi-twitter-x"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="LinkedIn"><i
                  class="bi bi-linkedin"></i></a>
            </div>
          </div>
          <div class="col-12">
            <hr class="border-warning">
            <p class="text-center mb-0">&copy; 2024 Decor&More. All rights
              reserved.</p>
          </div>
        </div>
      </div>
    </footer>

    <!-- Load libraries first -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Load our custom scripts -->
    <script src="/js/customAlerts.js"></script>
    <script src="/js/darkMode.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/script.js"></script>

    <script>
        function logout() {
          fetch("/logout", { method: "POST" }).then(() => (window.location.href = "/"));
        }
      </script>
    <script>
    document.getElementById('specializationFilter').addEventListener('submit', function(e) {
      e.preventDefault();
      const selectedSpecializations = Array.from(this.querySelectorAll('input[name="specializations"]:checked'))
        .map(checkbox => checkbox.value);
      
      const queryString = selectedSpecializations.length > 0 
        ? '?specializations=' + selectedSpecializations.join('&specializations=')
        : '';
        
      window.location.href = '/designers' + queryString;
    });
    </script>
  </body>
</html>