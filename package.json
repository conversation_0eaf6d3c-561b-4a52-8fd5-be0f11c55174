{"name": "decor-and-more", "version": "1.0.0", "main": "app.js", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"start": "node app.js", "watch": "nodemon --ext js,ejs", "dev": "cross-env NODE_ENV=development nodemon app.js", "validate-env": "node scripts/validateEnvironment.js", "railway-deploy": "npm run validate-env && railway up"}, "keywords": ["interior design", "booking", "designers", "decor", "architecture"], "author": "", "license": "ISC", "description": "A web application connecting interior designers with clients for booking services and managing projects", "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@google-cloud/vision": "^4.3.2", "@mui/material": "^6.1.1", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "cloudinary": "^2.7.0", "connect-flash": "^0.1.1", "connect-livereload": "^0.6.1", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "decor-and-more": "file:", "dotenv": "^16.4.7", "ejs": "^3.1.9", "express": "^4.21.2", "express-session": "^1.18.1", "express-validator": "^7.2.1", "google-auth-library": "^9.15.1", "helmet": "^8.0.0", "i18next": "^24.1.2", "intl-tel-input": "^25.3.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.11.20", "livereload": "^0.9.3", "method-override": "^3.0.0", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "mongodb": "^6.12.0", "mongodb-connection-string-url": "^3.0.2", "mongoose": "^8.9.7", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.0", "openai": "^4.83.0", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "serve-favicon": "^2.5.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "stripe": "^17.7.0"}, "devDependencies": {"cross-env": "^7.0.3", "ejs-lint": "^2.0.1", "nodemon": "^3.1.9", "webpack": "^5.97.1", "webpack-cli": "^6.0.1"}}