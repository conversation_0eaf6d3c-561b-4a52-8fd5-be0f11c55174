<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Login</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet">
    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/public.css">

    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">

    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- إضافة رابط Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
      rel="stylesheet">
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
      rel="stylesheet">
    <link
      href="https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap"
      rel="stylesheet">

    <!-- fonts  -->
    <link href="https://fonts.googleapis.com/css2?family=Belleza&display=swap"
      rel="stylesheet">
    <link
      href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap"
      rel="stylesheet">

    <link rel="stylesheet" href="/css/darkMode.css">
    <link rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">

    <style>
        .card {
    border-radius: var(--radius);
    background: var(--card);
    box-shadow: var(--shadow);
}

.form-control {
    border-color: var(--border);
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: var(--ring);
    box-shadow: 0 0 0 2px rgba(0, 112, 243, 0.1);
}

.password-field {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--foreground);
    opacity: 0.5;
    padding: 0.5rem;
}

.password-toggle:hover {
    opacity: 1;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
    color: var(--primary-foreground);
}

.btn-primary:hover {
    opacity: 0.9;
}

.form-floating > label {
    color: var(--foreground);
    opacity: 0.7;
}

.form-check-input:checked {
    background-color: var(--primary);
    border-color: var(--primary);
}

@media (max-width: 768px) {
    .card-body {
        padding: 2rem !important;
    }
}

    </style>

  </head>
  <body>
    <style>

      .dropdown {
          position: relative;
      }

      .dropdown-menu {
          position: absolute;
          top: 100%;
          left: 0;
          background: white;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border-radius: 5px;
          display: none;
          flex-direction: column;
          min-width: 150px;
      }

      .dropdown-menu a {
          display: block;
          padding: 10px;
          color: black;
          text-decoration: none;
      }

      .dropdown-menu a:hover {
          background: #f4f4f4;
      }


      .dropdown:hover .dropdown-menu {
          display: flex;
      }

      
      .logo-hero {
    background: linear-gradient(90deg, #d4af37 0%, #ffd700 50%, #bfa14a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    font-weight: bold;
}
      </style>
    <div class="container" data-aos="zoom-in" data-aos-duration="1200">
      <div class="row">
        <div
          class="navv-bar py-4 d-flex justify-content-between align-items-center">
          <!-- Logo Section -->
          <div class="logo-hero" style="font-size: 35px;" data-aos="fade-down"
            data-aos-duration="1200">
            Decore&More
          </div>
          <!-- Menu Button -->
          <div class="menu-btn d-lg-none" data-aos="fade-up"
            data-aos-duration="1200">
            <i class="fa-solid fa-bars"></i>
          </div>
          <!-- Navigation Links -->
          <div class="nav-links" data-aos="fade-up" data-aos-duration="1200">
            <ul class="list-unstyled m-0">
              <li><a href="/" class="nav-link">Home</a></li>
              <li><a href="/#op" class="nav-link">About</a></li>
              <li class="dropdown">
                <a href="/#od" class="nav-link">Services</a>
                <div class="dropdown-menu">
                  <a href="/packages/by-occasion?occasion=Birthday"
                    class="nav-link">Birthday</a>
                  <a href="/packages/by-occasion?occasion=Wedding"
                    class="nav-link">Wedding</a>
                  <a href="/packages/by-occasion?occasion=Engagement"
                    class="nav-link">Engagement</a>
                  <a href="/packages/by-occasion?occasion=BabyShower"
                    class="nav-link">BabyShower</a>
                </div>
              </li>
              <li><a href="/designers" class="nav-link">Designers</a></li>
              <% if (!user || !user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link">Register</a>
                <div class="dropdown-menu">
                  <a href="/registerCustomer">Customer</a>
                  <a href="/register">Engineer</a>
                </div>
              </li>
              <% } %>

              <% if (!user || !user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link">Login</a>
                <div class="dropdown-menu">
                  <a href="/login">Customer</a>
                  <a href="/login">Engineer</a>
                </div>
              </li>
              <% } %>

              <li><a href="/contact" class="nav-link">Contact</a></li>

              <% if (user && user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link d-flex align-items-center">
                  <i class="fa-solid fa-user"></i>
                  <span><%= user.name %></span>
                </a>
                <div class="dropdown-menu">
                  <a href="/userProfile/<%= user.id %>" class="nav-link">
                    <i class="fas fa-user me-2"></i>Profile
                  </a>
                  <a href="#" class="nav-link" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                  </a>
                </div>
              </li>
              <% }else{ %>

              <% } %>
              <button onclick="toggleDarkMode()" class="dark-mode"
                aria-label="Toggle dark mode">
                <i class="fa-solid fa-moon" aria-hidden="true"
                  title="Toggle to dark mode"></i>
              </button>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!-- end navbar -->

    <div class="container-fluid vh-100">
      <div class="row h-100 align-items-center justify-content-center">
        <div class="col-11 col-sm-8 col-md-6 col-lg-4">
          <div class="card border-0 shadow-lg">
            <div class="card-body p-5">
              <div class="text-center mb-4">
                <h4 class="fw-bold section-gradient-title">Welcome Back!</h4>
                <p class="text-muted">Please login to your account</p>
              </div>
              <form action="/login" method="post" id="loginForm" novalidate
                style="font-family: sans-serif;">
                <div class="form-floating mb-3">
                  <input type="email" name="email" class="form-control"
                    id="email" placeholder="<EMAIL>" required>
                  <label for="emailInput"><i
                      class="bi bi-envelope me-2"></i>Email address</label>
                  <div class="invalid-feedback">Please enter a valid email
                    address</div>
                </div>
                <div class="form-floating mb-3 password-field"
                  style="font-family: sans-serif;">
                  <input type="password" name="password" class="form-control"
                    id="password" placeholder="Password" required>
                  <label for="passwordInput"><i
                      class="bi bi-lock me-2"></i>Password</label>
                  <button type="button" class="btn btn-link password-toggle"
                    onclick="togglePassword()">
                    <i class="bi bi-eye"></i>
                  </button>
                  <div class="invalid-feedback">Password is required</div>
                </div>
            

                </div>
                <button type="submit" class="btn  w-100 mb-3"
                  style="background-color: goldenrod; color: white;">
                  <span class="spinner-border spinner-border-sm d-none me-2"
                    role="status"></span>
                  Login
                </button>
                <a href="/forgetPassword"
                  style="color: goldenrod; font-weight: 500;">Forget
                  password</a>

                <div id="alertBox"
                  style="display: none; font-weight: bold; margin-top: 10px;"></div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      function logout() {
      fetch("/logout", { method: "POST" }).then(() => (window.location.href = "/login"));
    }
  </script>
    <style>


      footer a:hover {
          opacity: 0.8;
          transition: all 0.3s ease;
      }

      footer {
          border-top: 2px solid var(--border);
      }

      footer h5 {
          font-weight: 600;
          letter-spacing: 1px;
      }

      footer .social-links a:hover {
          transform: translateY(-3px);
      }

      footer .list-unstyled li a:hover {
          padding-left: 5px;
      }

      footer .container {
          max-width: 1200px;
      }

      @media (max-width: 768px) {
          footer h2.display-4 {
              font-size: 2.5rem;
          }

          footer .col-md-4 {
              text-align: center;
          }

          footer .d-flex.gap-3 {
              justify-content: center;
          }
      }
      </style>

    <footer class="bg-dark text-warning py-5">
      <div class="container">
        <div class="row g-4">
          <div class="col-12 text-center mb-4">
            <h2 class="display-4 fw-bold">Decor&More</h2>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Contact Us</h5>
            <div class="mt-3">
              <p><i
                  class="bi bi-envelope-fill me-2"></i><EMAIL></p>
              <a href="https://web.whatsapp.com/send?phone=201556159175"
                target="_blank">
                <p><i class="bi bi-telephone-fill me-2"></i>+20 1556159175</p>
              </a>
              <p><i class="bi bi-geo-alt-fill me-2"></i>123 Decor Street, Design
                City</p>
            </div>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Quick Links</h5>
            <ul class="list-unstyled mt-3">
              <li class="mb-2"><a href="/"
                  class="text-warning text-decoration-none">Home</a></li>
              <li class="mb-2"><a href="/#op"
                  class="text-warning text-decoration-none">About</a></li>
              <% if (!user) { %>
              <li class="mb-2"><a href="/login"
                  class="text-warning text-decoration-none">login</a></li>
              <li class="mb-2"><a href="/register"
                  class="text-warning text-decoration-none">register</a></li>
              <% } %>
            </ul>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Follow Us</h5>
            <div class="mt-3 d-flex gap-3">
              <a href="#" class="text-warning fs-4" aria-label="Facebook"><i
                  class="bi bi-facebook"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="Instagram"><i
                  class="bi bi-instagram"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="Twitter"><i
                  class="bi bi-twitter-x"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="LinkedIn"><i
                  class="bi bi-linkedin"></i></a>
            </div>
          </div>
          <div class="col-12">
            <hr class="border-warning">
            <p class="text-center mb-0">&copy; 2024 Decor&More. All rights
              reserved.</p>
          </div>
        </div>
      </div>
    </footer>

    <!-- Load libraries first -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Load our custom scripts in proper order -->
    <script src="/js/customAlerts.js"></script>
    <script src="/js/register.js"></script>
    <script src="/js/login.js"></script>
    <script src="/js/darkMode.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/script.js"></script>

    <script>
  function togglePassword() {
    const passwordInput = document.getElementById("password");
    const toggleIcon = document.querySelector(".password-toggle i");

    if (passwordInput.type === "password") {
      passwordInput.type = "text";
      toggleIcon.classList.remove("bi-eye");
      toggleIcon.classList.add("bi-eye-slash");
    } else {
      passwordInput.type = "password";
      toggleIcon.classList.remove("bi-eye-slash");
      toggleIcon.classList.add("bi-eye");
    }
  }
</script>

  </body>
</html>